# 测试分析

import asyncio
import sys
import os
import json
import time
import pandas as pd

# 设置项目根目录到Python路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.database.db_mysql_analysis import get_sea_air_profit_from_tokens_table_cached, get_job_details_from_tokens_table_cached
from utils.basic.enhanced_analysis_export import export_analysis_result

async def test_optimized_function():
    """测试优化函数"""
    print("=== 测试优化函数 ===")
    begin_time = time.time()
    result_booking = await get_sea_air_profit_from_tokens_table_cached(begin_date="2022-01-01", end_date="2025-06-30")
    
    export_urls_booking = await export_analysis_result(
        result_booking,
        "excel"
    )
    print(f"Excel链接: {export_urls_booking.get('excel_url_booking', '未生成')}")

    result_job = await get_job_details_from_tokens_table_cached(begin_date="2022-01-01", end_date="2025-06-30")
    
    export_urls_job = await export_analysis_result(
        result_job,
        "excel"
    )
    print(f"Excel链接: {export_urls_job.get('excel_url_job', '未生成')}")   
    
    end_time = time.time()
    print(f"总耗时: {end_time - begin_time:.2f} 秒")
    
    return export_urls_booking, export_urls_job, end_time - begin_time

if __name__ == "__main__":
    result = asyncio.run(test_optimized_function())
    print('---')
    print(f'Booking导出链接: {result[0]}')
    print(f'Job导出链接: {result[1]}')
    print(f'总耗时: {result[2]:.2f} 秒')
    print('---')
