# MCP-CMS 部署与维护指南

本指南提供 MCP-CMS 系统的完整部署和日常维护流程说明。

## 📋 目录

- [服务器环境](#服务器环境)
- [重新部署流程](#重新部署流程)
- [日常代码维护](#日常代码维护)
- [脚本使用说明](#脚本使用说明)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🖥️ 服务器环境

### 服务器列表
| 服务器代码 | 地址 | 用户 | 路径 | 服务名 |
|-----------|------|------|------|-------|
| qd | qd.cmsgroup.com.cn | victorlee | /home/<USER>/mcp-cms/ | mcp-cms |
| sha | sh.cmsgroup.com.cn | victorlee | /home/<USER>/mcp-cms/ | mcp-cms |
| tyo | jp-server.cmslogistics.info | root | /root/mcp-cms/ | mcp-cms |
| hkg | cmshk-aliyun.ddns.net | root | /root/mcp-cms/ | mcp-cms |

### 本地环境要求
- 需要安装 `sshpass`: `brew install sshpass`
- 确保本地项目路径: `/Users/<USER>/x-code-files/mcp-cms`

## 🚀 重新部署流程

### 快速重新部署

✅ **推荐**: 用于已成功初始化过的环境

```bash
# 重新初始化所有服务器
./scripts/reinit.sh

# 仅重新初始化指定服务器
./scripts/reinit.sh --servers qd,sha

# 仅传输代码，不重建虚拟环境（最快）
./scripts/reinit.sh --skip-env-rebuild

# 仅重新初始化境内服务器
./scripts/reinit.sh --servers qd,sha
```

#### 快速重新部署流程包括：
1. 检查服务器连接
2. 停止所有服务
3. 传输最新代码
4. 重建虚拟环境（可选）
5. 重启服务

## 🔄 日常代码维护

### 日常代码更新（最常用）

```bash
# 正常传输文件到所有服务器
./scripts/transfer_fixed.sh

# 传输文件并清空测试数据表
./scripts/transfer_fixed.sh --clear-tables

# 使用配置文件传输
./scripts/transfer_fixed.sh config.conf
```

#### 日常更新流程包括：
1. 检查SSH连接
2. 停止远程服务
3. 创建文件排除列表
4. 使用rsync同步文件
5. 清理Python缓存
6. 重启服务
7. 验证服务状态

### 服务管理

```bash
# 启动所有服务
./scripts/start_all_services.sh

# 停止所有服务
./scripts/stop_all_services.sh

# 检查所有服务状态
./scripts/check_all_services.sh

# 检查服务日志
./scripts/check_service_logs.sh
```

## 📜 脚本使用说明

### 主要部署脚本

#### `scripts/reinit.sh` - 快速重新部署
```bash
用法: ./scripts/reinit.sh [配置文件] [选项]

选项:
  --servers SERVER_LIST    指定服务器（用逗号分隔，如：qd,sha）
  --skip-env-rebuild       跳过虚拟环境重建（仅传输代码）
  --help                   显示帮助信息

示例:
  ./scripts/reinit.sh                       # 重新初始化所有服务器
  ./scripts/reinit.sh --servers qd,sha      # 仅重新初始化指定服务器
  ./scripts/reinit.sh --skip-env-rebuild    # 仅传输代码，不重建环境
```

#### `scripts/transfer_fixed.sh` - 日常代码更新
```bash
用法: ./scripts/transfer_fixed.sh [配置文件] [选项]

选项:
  --clear-tables    清空共享测试数据表
  --help           显示帮助信息

示例:
  ./scripts/transfer_fixed.sh                           # 正常传输文件
  ./scripts/transfer_fixed.sh --clear-tables            # 传输文件并清空测试表
  ./scripts/transfer_fixed.sh config.conf --clear-tables # 使用配置文件并清空测试表
```

### 服务管理脚本

#### `scripts/start_all_services.sh` - 启动所有服务
```bash
./scripts/start_all_services.sh
```

#### `scripts/stop_all_services.sh` - 停止所有服务
```bash
./scripts/stop_all_services.sh
```

#### `scripts/check_all_services.sh` - 检查服务状态
```bash
./scripts/check_all_services.sh
```

## 🔧 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查网络连接
ping qd.cmsgroup.com.cn

# 手动测试SSH连接
sshpass -p "2929!lxj#LXJ" ssh <EMAIL>
```

#### 2. 服务启动失败
```bash
# 检查服务状态
./scripts/check_all_services.sh

# 查看服务日志
./scripts/check_service_logs.sh

# 手动检查特定服务器
sshpass -p "2929!lxj#LXJ" ssh <EMAIL> "systemctl status mcp-cms"
```

#### 3. 虚拟环境问题
```bash
# 重建虚拟环境
./scripts/reinit.sh

# 或手动重建（登录服务器后）
cd /home/<USER>/mcp-cms/
rm -rf .venv
~/.local/bin/uv venv .venv
~/.local/bin/uv sync
```

#### 4. 文件传输失败
```bash
# 检查本地路径
ls -la /Users/<USER>/x-code-files/mcp-cms

# 检查磁盘空间
./scripts/check_all_services.sh
```

### 日志查看

#### 系统服务日志
```bash
# 查看服务日志
journalctl -u mcp-cms -f

# 查看最近的错误日志
journalctl -u mcp-cms --since "1 hour ago" -p err
```

#### 应用日志
```bash
# 应用日志文件位置
ls -la logs/
tail -f logs/fastapi_cms_simplified.log
```

## 📈 最佳实践

### 1. 日常开发流程
```bash
# 1. 本地代码修改完成后
# 2. 提交到版本控制
git add .
git commit -m "描述修改内容"

# 3. 部署到生产环境
./scripts/transfer_fixed.sh

# 4. 验证部署结果
./scripts/check_all_services.sh
```

### 2. 重大更新流程
```bash
# 1. 备份当前环境（可选）
# 2. 使用快速重新初始化
./scripts/reinit.sh

# 3. 验证所有服务
./scripts/check_all_services.sh

# 4. 检查应用功能
```

### 3. 服务器选择策略

#### 境内服务器（qd, sha）
- 网络延迟低
- 适合频繁更新
- 优先用于测试

#### 境外服务器（tyo, hkg）
- 网络可能较慢
- 建议批量更新
- 用于生产服务

### 4. 性能优化建议

#### 快速部署选项
```bash
# 仅更新代码，不重建环境（最快）
./scripts/reinit.sh --skip-env-rebuild

# 仅更新特定服务器
./scripts/reinit.sh --servers qd,sha

# 日常代码更新（推荐）
./scripts/transfer_fixed.sh
```

#### 定期维护
```bash
# 每周清理测试数据
./scripts/transfer_fixed.sh --clear-tables

# 每月完全重新初始化（可选）
./scripts/reinit.sh
```

## 🔄 更新历史

| 日期 | 版本 | 变更内容 |
|------|------|----------|
| 2024-12 | v1.0 | 初始版本，整合所有部署脚本 |

---

**注意**: 所有脚本都已经过测试并在生产环境中成功运行。如遇问题，请按照故障排除指南进行检查。 