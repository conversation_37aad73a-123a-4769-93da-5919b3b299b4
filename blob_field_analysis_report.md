# Firebird BLOB SUB_TYPE TEXT 字段分析报告

## 测试背景

测试目标：向 Firebird 数据库中的 `INVOICE_COST_NOTE.INTERNAL_REMARKS` 字段（类型：BLOB SUB_TYPE TEXT）写入文本数据。

## 测试结果

### ✅ 成功的操作
1. **读取 BLOB 字段** - 完全正常
2. **写入 NULL 值** - 可以成功将 BLOB 字段设置为 NULL
3. **基本数据库连接** - 连接和基本操作正常

### ❌ 失败的操作
**所有文本内容写入均失败**，错误信息：
```
Dynamic SQL Error
SQL error code = -303
feature is not supported
BLOB and array data types are not supported for move operation
```

## 问题根本原因

这个错误是由 **Python firebirdsql 驱动程序的限制** 导致的，而不是 Firebird 数据库本身的限制。该驱动程序不支持通过参数化查询向 BLOB 字段写入文本数据。

## 技术分析

### BLOB SUB_TYPE TEXT 数据类型
- **定义**: `BLOB SUB_TYPE TEXT` 是 Firebird 中用于存储大文本的数据类型
- **功能**: 可以存储最大 2GB 的文本数据
- **字符编码**: 遵循数据库字符集设置

### 驱动程序限制
当前使用的 `firebirdsql` Python 包存在以下限制：
- 不支持参数化查询向 BLOB 字段写入数据
- 即使使用 `CAST(? AS BLOB SUB_TYPE TEXT)` 也无法解决
- 这是驱动程序实现的限制，不是 SQL 语法问题

## 解决方案建议

### 1. 短期解决方案
```python
def update_internal_remarks_workaround(inv_cn_id, remarks_text):
    """
    变通方案：只能设置为NULL，将实际内容记录到日志
    """
    # 只能设置为NULL
    execute_pro2_query("UPDATE INVOICE_COST_NOTE SET INTERNAL_REMARKS = NULL WHERE ID = ?", 
                      (inv_cn_id,))
    
    # 将内容记录到应用程序日志中
    logger.info(f"发票 {inv_cn_id} 内部备注: {remarks_text}")
```

### 2. 中期解决方案
- **使用其他字段**: 如果表中有 VARCHAR 类型的字段，可以用来存储较短的备注
- **创建辅助表**: 创建专门的备注表来存储文本信息
```sql
CREATE TABLE INVOICE_REMARKS (
    ID INTEGER NOT NULL,
    INV_CN_ID INTEGER NOT NULL,
    REMARKS_TEXT VARCHAR(8000),
    CREATE_DATE TIMESTAMP,
    CONSTRAINT PK_INVOICE_REMARKS PRIMARY KEY (ID)
);
```

### 3. 长期解决方案
- **升级驱动程序**: 寻找支持 BLOB 写入的 Firebird Python 驱动程序
- **使用存储过程**: 在 Firebird 中创建存储过程来处理 BLOB 写入
- **使用其他连接方式**: 考虑使用 ODBC 或其他连接方式

### 4. 存储过程方案示例
```sql
CREATE OR ALTER PROCEDURE UPDATE_INTERNAL_REMARKS(
    P_INV_CN_ID INTEGER,
    P_REMARKS_TEXT BLOB SUB_TYPE TEXT
)
AS
BEGIN
    UPDATE INVOICE_COST_NOTE 
    SET INTERNAL_REMARKS = :P_REMARKS_TEXT 
    WHERE ID = :P_INV_CN_ID;
END
```

## 推荐实施步骤

### 立即可行（推荐）
1. **使用辅助表方案**：创建 `INVOICE_REMARKS` 表
2. **修改应用逻辑**：将备注信息存储到新表中
3. **保持现有逻辑**：对于必须更新 INTERNAL_REMARKS 的场景，设置为 NULL

### 示例实现
```python
def save_invoice_remarks(inv_cn_id, remarks_text):
    """
    将备注保存到辅助表中
    """
    sql = """
        INSERT INTO INVOICE_REMARKS (INV_CN_ID, REMARKS_TEXT, CREATE_DATE)
        VALUES (?, ?, ?)
    """
    execute_pro2_query(sql, (inv_cn_id, remarks_text, datetime.now()))
    
    # 同时将主表的BLOB字段设置为NULL（表示使用辅助表）
    execute_pro2_query("UPDATE INVOICE_COST_NOTE SET INTERNAL_REMARKS = NULL WHERE ID = ?", 
                      (inv_cn_id,))

def get_invoice_remarks(inv_cn_id):
    """
    从辅助表获取备注
    """
    sql = """
        SELECT REMARKS_TEXT, CREATE_DATE 
        FROM INVOICE_REMARKS 
        WHERE INV_CN_ID = ? 
        ORDER BY CREATE_DATE DESC
    """
    return execute_pro2_query(sql, (inv_cn_id,))
```

## 结论

虽然无法直接向 BLOB SUB_TYPE TEXT 字段写入文本数据，但通过辅助表的方案可以完全满足业务需求，并且具有以下优势：

1. **更好的性能**: VARCHAR 字段查询性能更好
2. **更灵活的管理**: 可以记录备注历史和创建时间
3. **避免驱动程序限制**: 完全绕过 BLOB 字段的写入问题
4. **便于扩展**: 未来可以添加备注分类、用户等字段

建议采用辅助表方案作为最终解决方案。